module github.com/meizu/mcp-simply-fetch

go 1.21

require (
	github.com/mark3labs/mcp-go v0.6.0
	github.com/playwright-community/playwright-go v0.4702.0
	github.com/PuerkitoBio/goquery v1.8.1
	github.com/go-shiori/go-readability v0.0.0-20231029095239-6b97d5aba789
	github.com/mattn/go-sqlite3 v1.14.18
	github.com/patrickmn/go-cache v2.1.0+incompatible
	github.com/spf13/viper v1.17.0
	github.com/spf13/cobra v1.8.0
	github.com/sirupsen/logrus v1.9.3
	golang.org/x/sync v0.5.0
	golang.org/x/net v0.19.0
)

require (
	github.com/andybalholm/cascadia v1.3.1 // indirect
	github.com/araddon/dateparse v0.0.0-20210429162001-6b43995a97de // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/go-jose/go-jose/v3 v3.0.1 // indirect
	github.com/go-shiori/dom v0.0.0-20230515143342-73569d674e1c // indirect
	github.com/gogs/chardet v0.0.0-20211120154057-b7413eaefb8f // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/pelletier/go-toml/v2 v2.1.0 // indirect
	github.com/spf13/afero v1.10.0 // indirect
	github.com/spf13/cast v1.5.0 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	golang.org/x/sys v0.15.0 // indirect
	golang.org/x/text v0.14.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
