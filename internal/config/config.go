package config

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/spf13/viper"
)

// Config 主配置结构
type Config struct {
	Server  ServerConfig  `yaml:"server" mapstructure:"server"`
	Proxy   ProxyConfig   `yaml:"proxy" mapstructure:"proxy"`
	Auth    AuthConfig    `yaml:"auth" mapstructure:"auth"`
	Cache   CacheConfig   `yaml:"cache" mapstructure:"cache"`
	Fetcher FetcherConfig `yaml:"fetcher" mapstructure:"fetcher"`
	AntiBot AntiBotConfig `yaml:"anti_bot" mapstructure:"anti_bot"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Name       string `yaml:"name" mapstructure:"name"`
	Version    string `yaml:"version" mapstructure:"version"`
	LogLevel   string `yaml:"log_level" mapstructure:"log_level"`
	MaxWorkers int    `yaml:"max_workers" mapstructure:"max_workers"`
}

// ProxyConfig 代理配置
type ProxyConfig struct {
	Enabled    bool          `yaml:"enabled" mapstructure:"enabled"`
	AutoDetect bool          `yaml:"auto_detect" mapstructure:"auto_detect"`
	ProxyList  []string      `yaml:"proxy_list" mapstructure:"proxy_list"`
	RotateMode string        `yaml:"rotate_mode" mapstructure:"rotate_mode"`
	Timeout    time.Duration `yaml:"timeout" mapstructure:"timeout"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	StorePath string                    `yaml:"store_path" mapstructure:"store_path"`
	Domains   map[string]DomainAuthInfo `yaml:"domains" mapstructure:"domains"`
}

// DomainAuthInfo 域名认证信息
type DomainAuthInfo struct {
	Type        string            `yaml:"type" mapstructure:"type"` // cookie, basic, bearer
	Credentials map[string]string `yaml:"credentials" mapstructure:"credentials"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Enabled     bool          `yaml:"enabled" mapstructure:"enabled"`
	DatabaseURL string        `yaml:"database_url" mapstructure:"database_url"`
	TTL         time.Duration `yaml:"ttl" mapstructure:"ttl"`
	MaxSize     int64         `yaml:"max_size" mapstructure:"max_size"`
	CleanupInterval time.Duration `yaml:"cleanup_interval" mapstructure:"cleanup_interval"`
}

// FetcherConfig 抓取器配置
type FetcherConfig struct {
	UserAgents   []string      `yaml:"user_agents" mapstructure:"user_agents"`
	Timeout      time.Duration `yaml:"timeout" mapstructure:"timeout"`
	MaxRetries   int           `yaml:"max_retries" mapstructure:"max_retries"`
	RetryDelay   time.Duration `yaml:"retry_delay" mapstructure:"retry_delay"`
	MaxRedirects int           `yaml:"max_redirects" mapstructure:"max_redirects"`
	Concurrency  int           `yaml:"concurrency" mapstructure:"concurrency"`
}

// AntiBotConfig 反爬虫配置
type AntiBotConfig struct {
	Enabled           bool          `yaml:"enabled" mapstructure:"enabled"`
	RandomDelay       bool          `yaml:"random_delay" mapstructure:"random_delay"`
	MinDelay          time.Duration `yaml:"min_delay" mapstructure:"min_delay"`
	MaxDelay          time.Duration `yaml:"max_delay" mapstructure:"max_delay"`
	RotateUserAgent   bool          `yaml:"rotate_user_agent" mapstructure:"rotate_user_agent"`
	SimulateBehavior  bool          `yaml:"simulate_behavior" mapstructure:"simulate_behavior"`
	CaptchaServiceURL string        `yaml:"captcha_service_url" mapstructure:"captcha_service_url"`
}

// Load 加载配置文件
func Load(configFile string) (*Config, error) {
	v := viper.New()
	
	// 设置默认值
	setDefaults(v)
	
	// 配置文件路径
	if configFile != "" {
		v.SetConfigFile(configFile)
	} else {
		// 默认配置文件位置
		v.SetConfigName("config")
		v.SetConfigType("yaml")
		v.AddConfigPath(".")
		v.AddConfigPath("./configs")
		v.AddConfigPath("$HOME/.mcp-simply-fetch")
		v.AddConfigPath("/etc/mcp-simply-fetch")
	}
	
	// 环境变量支持
	v.SetEnvPrefix("MCP_FETCH")
	v.AutomaticEnv()
	
	// 读取配置文件
	if err := v.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
		// 配置文件不存在时使用默认配置
	}
	
	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	
	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}
	
	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults(v *viper.Viper) {
	// 服务器默认配置
	v.SetDefault("server.name", "MCP Simply Fetch")
	v.SetDefault("server.version", "1.0.0")
	v.SetDefault("server.log_level", "info")
	v.SetDefault("server.max_workers", 10)
	
	// 代理默认配置
	v.SetDefault("proxy.enabled", false)
	v.SetDefault("proxy.auto_detect", true)
	v.SetDefault("proxy.rotate_mode", "round_robin")
	v.SetDefault("proxy.timeout", "30s")
	
	// 认证默认配置
	v.SetDefault("auth.store_path", "./data/auth.db")
	
	// 缓存默认配置
	v.SetDefault("cache.enabled", true)
	v.SetDefault("cache.database_url", "./data/cache.db")
	v.SetDefault("cache.ttl", "24h")
	v.SetDefault("cache.max_size", 1024*1024*100) // 100MB
	v.SetDefault("cache.cleanup_interval", "1h")
	
	// 抓取器默认配置
	v.SetDefault("fetcher.timeout", "30s")
	v.SetDefault("fetcher.max_retries", 3)
	v.SetDefault("fetcher.retry_delay", "1s")
	v.SetDefault("fetcher.max_redirects", 10)
	v.SetDefault("fetcher.concurrency", 5)
	v.SetDefault("fetcher.user_agents", []string{
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
	})
	
	// 反爬虫默认配置
	v.SetDefault("anti_bot.enabled", true)
	v.SetDefault("anti_bot.random_delay", true)
	v.SetDefault("anti_bot.min_delay", "500ms")
	v.SetDefault("anti_bot.max_delay", "2s")
	v.SetDefault("anti_bot.rotate_user_agent", true)
	v.SetDefault("anti_bot.simulate_behavior", true)
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	// 验证数据目录
	dataDir := filepath.Dir(config.Cache.DatabaseURL)
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		return fmt.Errorf("failed to create data directory: %w", err)
	}
	
	// 验证并发数
	if config.Fetcher.Concurrency <= 0 {
		config.Fetcher.Concurrency = 1
	}
	if config.Fetcher.Concurrency > 50 {
		config.Fetcher.Concurrency = 50
	}
	
	// 验证重试次数
	if config.Fetcher.MaxRetries < 0 {
		config.Fetcher.MaxRetries = 0
	}
	if config.Fetcher.MaxRetries > 10 {
		config.Fetcher.MaxRetries = 10
	}
	
	return nil
}
