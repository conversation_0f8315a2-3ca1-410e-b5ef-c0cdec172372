package engines

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/go-shiori/go-readability"
	"github.com/meizu/mcp-simply-fetch/internal/config"
	"github.com/meizu/mcp-simply-fetch/internal/services"
	"github.com/playwright-community/playwright-go"
	"github.com/sirupsen/logrus"
)

// DynamicEngine 动态抓取引擎（使用Playwright）
type DynamicEngine struct {
	pw            *playwright.Playwright
	browser       playwright.Browser
	config        config.FetcherConfig
	antiBotConfig config.AntiBotConfig
	userAgents    []string
	currentUA     int
}

// NewDynamicEngine 创建动态抓取引擎
func NewDynamicEngine(fetcherConfig config.FetcherConfig, antiBotConfig config.AntiBotConfig) (*DynamicEngine, error) {
	return &DynamicEngine{
		config:        fetcherConfig,
		antiBotConfig: antiBotConfig,
		userAgents:    fetcherConfig.UserAgents,
	}, nil
}

// Start 启动引擎
func (e *DynamicEngine) Start(ctx context.Context) error {
	logrus.Info("Starting dynamic engine...")
	
	// 启动Playwright
	pw, err := playwright.Run()
	if err != nil {
		return fmt.Errorf("failed to start playwright: %w", err)
	}
	e.pw = pw
	
	// 启动浏览器
	browser, err := pw.Chromium.Launch(playwright.BrowserTypeLaunchOptions{
		Headless: playwright.Bool(true),
		Args: []string{
			"--no-sandbox",
			"--disable-setuid-sandbox",
			"--disable-dev-shm-usage",
			"--disable-accelerated-2d-canvas",
			"--no-first-run",
			"--no-zygote",
			"--disable-gpu",
		},
	})
	if err != nil {
		pw.Stop()
		return fmt.Errorf("failed to launch browser: %w", err)
	}
	e.browser = browser
	
	logrus.Info("Dynamic engine started")
	return nil
}

// Stop 停止引擎
func (e *DynamicEngine) Stop(ctx context.Context) error {
	logrus.Info("Stopping dynamic engine...")
	
	if e.browser != nil {
		if err := e.browser.Close(); err != nil {
			logrus.Errorf("Failed to close browser: %v", err)
		}
	}
	
	if e.pw != nil {
		if err := e.pw.Stop(); err != nil {
			logrus.Errorf("Failed to stop playwright: %v", err)
		}
	}
	
	logrus.Info("Dynamic engine stopped")
	return nil
}

// Fetch 抓取URL
func (e *DynamicEngine) Fetch(
	ctx context.Context,
	options *services.FetchOptions,
	authService AuthServiceInterface,
	proxyService ProxyServiceInterface,
) (*services.FetchResult, error) {
	
	startTime := time.Now()
	
	if e.browser == nil {
		return nil, fmt.Errorf("browser not initialized")
	}
	
	// 创建浏览器上下文
	contextOptions := playwright.BrowserNewContextOptions{}
	
	// 设置User-Agent
	userAgent := options.UserAgent
	if userAgent == "" {
		userAgent = e.getNextUserAgent()
	}
	contextOptions.UserAgent = &userAgent
	
	// 设置代理
	if options.UseProxy && proxyService != nil {
		if proxy := proxyService.GetNext(); proxy != nil {
			contextOptions.Proxy = &playwright.Proxy{
				Server: proxy.URL,
			}
		}
	}
	
	// 设置视口
	contextOptions.Viewport = &playwright.Size{
		Width:  1920,
		Height: 1080,
	}
	
	browserContext, err := e.browser.NewContext(contextOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to create browser context: %w", err)
	}
	defer browserContext.Close()
	
	// 创建页面
	page, err := browserContext.NewPage()
	if err != nil {
		return nil, fmt.Errorf("failed to create page: %w", err)
	}
	defer page.Close()
	
	// 设置超时
	timeout := float64(e.config.Timeout.Milliseconds())
	page.SetDefaultTimeout(timeout)
	page.SetDefaultNavigationTimeout(timeout)
	
	// 应用认证
	if authService != nil {
		if err := e.applyAuth(page, options.URL, authService); err != nil {
			logrus.Warnf("Failed to apply auth for %s: %v", options.URL, err)
		}
	}
	
	// 设置额外请求头
	if len(options.Headers) > 0 {
		if err := page.SetExtraHTTPHeaders(options.Headers); err != nil {
			logrus.Warnf("Failed to set extra headers: %v", err)
		}
	}
	
	// 反爬虫延迟
	if e.antiBotConfig.Enabled && e.antiBotConfig.RandomDelay {
		e.randomDelay()
	}
	
	// 导航到页面
	response, err := page.Goto(options.URL, playwright.PageGotoOptions{
		WaitUntil: playwright.WaitUntilStateNetworkidle,
		Timeout:   &timeout,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to navigate to page: %w", err)
	}
	
	// 等待页面加载完成
	if err := e.waitForPageLoad(page); err != nil {
		logrus.Warnf("Page load wait failed: %v", err)
	}
	
	// 模拟人类行为
	if e.antiBotConfig.Enabled && e.antiBotConfig.SimulateBehavior {
		if err := e.simulateHumanBehavior(page); err != nil {
			logrus.Warnf("Failed to simulate human behavior: %v", err)
		}
	}
	
	// 获取页面内容
	html, err := page.Content()
	if err != nil {
		return nil, fmt.Errorf("failed to get page content: %w", err)
	}
	
	// 获取状态码
	statusCode := 200
	if response != nil {
		statusCode = response.Status()
	}
	
	// 解析内容
	result, err := e.parseContent(html, options.URL, statusCode)
	if err != nil {
		return nil, fmt.Errorf("failed to parse content: %w", err)
	}
	
	result.Duration = time.Since(startTime)
	return result, nil
}

// getNextUserAgent 获取下一个User-Agent
func (e *DynamicEngine) getNextUserAgent() string {
	if len(e.userAgents) == 0 {
		return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
	}
	
	if e.antiBotConfig.Enabled && e.antiBotConfig.RotateUserAgent {
		e.currentUA = (e.currentUA + 1) % len(e.userAgents)
	}
	
	return e.userAgents[e.currentUA]
}

// applyAuth 应用认证
func (e *DynamicEngine) applyAuth(page playwright.Page, url string, authService AuthServiceInterface) error {
	domain := extractDomain(url)
	auth := authService.GetAuth(domain)
	if auth == nil {
		return nil
	}
	
	switch auth.Type {
	case "cookie":
		if cookies, ok := auth.Credentials["cookies"]; ok {
			// 解析cookie字符串并设置
			if err := e.setCookies(page, cookies, domain); err != nil {
				return fmt.Errorf("failed to set cookies: %w", err)
			}
		}
	case "basic", "bearer":
		// 对于基本认证和Bearer token，设置请求头
		headers := make(map[string]string)
		if auth.Type == "basic" {
			if username, ok := auth.Credentials["username"]; ok {
				if password, ok := auth.Credentials["password"]; ok {
					// 这里需要base64编码
					headers["Authorization"] = "Basic " + encodeBasicAuth(username, password)
				}
			}
		} else if auth.Type == "bearer" {
			if token, ok := auth.Credentials["token"]; ok {
				headers["Authorization"] = "Bearer " + token
			}
		}
		
		if len(headers) > 0 {
			if err := page.SetExtraHTTPHeaders(headers); err != nil {
				return fmt.Errorf("failed to set auth headers: %w", err)
			}
		}
	}
	
	return nil
}

// setCookies 设置cookies
func (e *DynamicEngine) setCookies(page playwright.Page, cookieStr, domain string) error {
	// 简单的cookie解析（实际应用中可能需要更复杂的解析）
	cookies := []playwright.BrowserContextAddCookiesOptionsCookies{}
	
	for _, cookie := range strings.Split(cookieStr, ";") {
		parts := strings.SplitN(strings.TrimSpace(cookie), "=", 2)
		if len(parts) == 2 {
			cookies = append(cookies, playwright.BrowserContextAddCookiesOptionsCookies{
				Name:   parts[0],
				Value:  parts[1],
				Domain: &domain,
			})
		}
	}
	
	if len(cookies) > 0 {
		return page.Context().AddCookies(cookies)
	}
	
	return nil
}

// waitForPageLoad 等待页面加载完成
func (e *DynamicEngine) waitForPageLoad(page playwright.Page) error {
	// 等待DOM加载完成
	if err := page.WaitForLoadState(playwright.LoadStateDomcontentloaded); err != nil {
		return err
	}
	
	// 等待网络空闲
	if err := page.WaitForLoadState(playwright.LoadStateNetworkidle); err != nil {
		return err
	}
	
	// 额外等待一些常见的加载指示器消失
	selectors := []string{
		".loading",
		".spinner",
		"[data-loading]",
		".loader",
	}
	
	for _, selector := range selectors {
		// 尝试等待加载指示器消失，但不要因为超时而失败
		page.WaitForSelector(selector, playwright.PageWaitForSelectorOptions{
			State:   playwright.WaitForSelectorStateHidden,
			Timeout: playwright.Float(5000), // 5秒超时
		})
	}
	
	return nil
}

// simulateHumanBehavior 模拟人类行为
func (e *DynamicEngine) simulateHumanBehavior(page playwright.Page) error {
	// 随机滚动
	if err := page.Evaluate(`
		window.scrollTo({
			top: Math.random() * document.body.scrollHeight * 0.3,
			behavior: 'smooth'
		});
	`); err != nil {
		return err
	}
	
	// 短暂等待
	time.Sleep(time.Duration(500+time.Now().UnixNano()%1000) * time.Millisecond)
	
	// 随机鼠标移动
	if err := page.Mouse().Move(
		float64(100+time.Now().UnixNano()%800),
		float64(100+time.Now().UnixNano()%600),
	); err != nil {
		return err
	}
	
	return nil
}

// randomDelay 随机延迟
func (e *DynamicEngine) randomDelay() {
	if e.antiBotConfig.MinDelay <= 0 || e.antiBotConfig.MaxDelay <= 0 {
		return
	}
	
	minMs := e.antiBotConfig.MinDelay.Milliseconds()
	maxMs := e.antiBotConfig.MaxDelay.Milliseconds()
	
	if maxMs <= minMs {
		time.Sleep(e.antiBotConfig.MinDelay)
		return
	}
	
	delayMs := minMs + (time.Now().UnixNano()%(maxMs-minMs))
	time.Sleep(time.Duration(delayMs) * time.Millisecond)
}

// parseContent 解析内容（复用静态引擎的解析逻辑）
func (e *DynamicEngine) parseContent(html, url string, statusCode int) (*services.FetchResult, error) {
	// 使用goquery解析HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(html))
	if err != nil {
		return nil, fmt.Errorf("failed to parse HTML: %w", err)
	}
	
	// 提取基本信息
	title := doc.Find("title").First().Text()
	title = strings.TrimSpace(title)
	
	// 使用readability提取主要内容
	article, err := readability.FromReader(strings.NewReader(html), url)
	if err != nil {
		logrus.Warnf("Readability extraction failed for %s: %v", url, err)
		article.TextContent = doc.Find("body").Text()
		article.Content = html
	}
	
	// 提取链接
	links := e.extractLinks(doc, url)
	
	// 提取图片
	images := e.extractImages(doc, url)
	
	// 提取元数据
	metadata := e.extractMetadata(doc)
	
	return &services.FetchResult{
		URL:        url,
		Title:      title,
		Content:    article.Content,
		PlainText:  strings.TrimSpace(article.TextContent),
		Metadata:   metadata,
		Links:      links,
		Images:     images,
		StatusCode: statusCode,
		FetchTime:  time.Now(),
	}, nil
}

// 复用静态引擎的解析方法
func (e *DynamicEngine) extractLinks(doc *goquery.Document, baseURL string) []services.Link {
	var links []services.Link
	
	doc.Find("a[href]").Each(func(i int, s *goquery.Selection) {
		href, exists := s.Attr("href")
		if !exists {
			return
		}
		
		absoluteURL := resolveURL(baseURL, href)
		if absoluteURL == "" {
			return
		}
		
		text := strings.TrimSpace(s.Text())
		title, _ := s.Attr("title")
		rel, _ := s.Attr("rel")
		
		links = append(links, services.Link{
			URL:   absoluteURL,
			Text:  text,
			Title: title,
			Rel:   rel,
		})
	})
	
	return links
}

func (e *DynamicEngine) extractImages(doc *goquery.Document, baseURL string) []services.Image {
	var images []services.Image
	
	doc.Find("img[src]").Each(func(i int, s *goquery.Selection) {
		src, exists := s.Attr("src")
		if !exists {
			return
		}
		
		absoluteURL := resolveURL(baseURL, src)
		if absoluteURL == "" {
			return
		}
		
		alt, _ := s.Attr("alt")
		title, _ := s.Attr("title")
		
		images = append(images, services.Image{
			URL:   absoluteURL,
			Alt:   alt,
			Title: title,
		})
	})
	
	return images
}

func (e *DynamicEngine) extractMetadata(doc *goquery.Document) map[string]string {
	metadata := make(map[string]string)
	
	doc.Find("meta").Each(func(i int, s *goquery.Selection) {
		name, nameExists := s.Attr("name")
		property, propertyExists := s.Attr("property")
		content, contentExists := s.Attr("content")
		
		if !contentExists {
			return
		}
		
		if nameExists {
			metadata[name] = content
		}
		if propertyExists {
			metadata[property] = content
		}
	})
	
	return metadata
}

// 辅助函数
func encodeBasicAuth(username, password string) string {
	// 简单的base64编码实现
	// 实际应用中应该使用标准库的base64包
	return username + ":" + password // 这里需要实际的base64编码
}
