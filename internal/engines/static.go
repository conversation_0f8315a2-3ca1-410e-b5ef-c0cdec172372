package engines

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/go-shiori/go-readability"
	"github.com/meizu/mcp-simply-fetch/internal/config"
	"github.com/meizu/mcp-simply-fetch/internal/services"
	"github.com/sirupsen/logrus"
)

// StaticEngine 静态抓取引擎
type StaticEngine struct {
	client        *http.Client
	config        config.FetcherConfig
	antiBotConfig config.AntiBotConfig
	userAgents    []string
	currentUA     int
}

// NewStaticEngine 创建静态抓取引擎
func NewStaticEngine(fetcherConfig config.FetcherConfig, antiBotConfig config.AntiBotConfig) (*StaticEngine, error) {
	// 创建HTTP客户端
	client := &http.Client{
		Timeout: fetcherConfig.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= fetcherConfig.MaxRedirects {
				return fmt.Errorf("too many redirects")
			}
			return nil
		},
	}

	return &StaticEngine{
		client:        client,
		config:        fetcherConfig,
		antiBotConfig: antiBotConfig,
		userAgents:    fetcherConfig.UserAgents,
	}, nil
}

// Start 启动引擎
func (e *StaticEngine) Start(ctx context.Context) error {
	logrus.Info("Starting static engine...")
	return nil
}

// Stop 停止引擎
func (e *StaticEngine) Stop(ctx context.Context) error {
	logrus.Info("Stopping static engine...")
	return nil
}

// Fetch 抓取URL
func (e *StaticEngine) Fetch(
	ctx context.Context,
	options *services.FetchOptions,
	authService AuthServiceInterface,
	proxyService ProxyServiceInterface,
) (*services.FetchResult, error) {
	
	startTime := time.Now()
	
	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "GET", options.URL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	
	// 设置请求头
	e.setRequestHeaders(req, options)
	
	// 应用认证
	if authService != nil {
		if err := e.applyAuth(req, authService); err != nil {
			logrus.Warnf("Failed to apply auth for %s: %v", options.URL, err)
		}
	}
	
	// 设置代理
	if options.UseProxy && proxyService != nil {
		if err := e.setProxy(proxyService); err != nil {
			logrus.Warnf("Failed to set proxy: %v", err)
		}
	}
	
	// 反爬虫延迟
	if e.antiBotConfig.Enabled && e.antiBotConfig.RandomDelay {
		e.randomDelay()
	}
	
	// 执行请求
	resp, err := e.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()
	
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	
	// 解析内容
	result, err := e.parseContent(string(body), options.URL, resp.StatusCode)
	if err != nil {
		return nil, fmt.Errorf("failed to parse content: %w", err)
	}
	
	result.Duration = time.Since(startTime)
	return result, nil
}

// setRequestHeaders 设置请求头
func (e *StaticEngine) setRequestHeaders(req *http.Request, options *services.FetchOptions) {
	// 设置User-Agent
	userAgent := options.UserAgent
	if userAgent == "" {
		userAgent = e.getNextUserAgent()
	}
	req.Header.Set("User-Agent", userAgent)
	
	// 设置常见请求头
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	
	// 设置自定义请求头
	for key, value := range options.Headers {
		req.Header.Set(key, value)
	}
}

// getNextUserAgent 获取下一个User-Agent
func (e *StaticEngine) getNextUserAgent() string {
	if len(e.userAgents) == 0 {
		return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
	}
	
	if e.antiBotConfig.Enabled && e.antiBotConfig.RotateUserAgent {
		e.currentUA = (e.currentUA + 1) % len(e.userAgents)
	}
	
	return e.userAgents[e.currentUA]
}

// applyAuth 应用认证
func (e *StaticEngine) applyAuth(req *http.Request, authService AuthServiceInterface) error {
	domain := extractDomain(req.URL.String())
	auth := authService.GetAuth(domain)
	if auth == nil {
		return nil
	}
	
	switch auth.Type {
	case "basic":
		if username, ok := auth.Credentials["username"]; ok {
			if password, ok := auth.Credentials["password"]; ok {
				req.SetBasicAuth(username, password)
			}
		}
	case "bearer":
		if token, ok := auth.Credentials["token"]; ok {
			req.Header.Set("Authorization", "Bearer "+token)
		}
	case "cookie":
		if cookies, ok := auth.Credentials["cookies"]; ok {
			req.Header.Set("Cookie", cookies)
		}
	case "custom":
		for key, value := range auth.Credentials {
			req.Header.Set(key, value)
		}
	}
	
	return nil
}

// setProxy 设置代理
func (e *StaticEngine) setProxy(proxyService ProxyServiceInterface) error {
	proxy := proxyService.GetNext()
	if proxy == nil {
		return nil
	}
	
	proxyURL, err := url.Parse(proxy.URL)
	if err != nil {
		return fmt.Errorf("invalid proxy URL: %w", err)
	}
	
	transport := &http.Transport{
		Proxy: http.ProxyURL(proxyURL),
	}
	
	e.client.Transport = transport
	return nil
}

// randomDelay 随机延迟
func (e *StaticEngine) randomDelay() {
	if e.antiBotConfig.MinDelay <= 0 || e.antiBotConfig.MaxDelay <= 0 {
		return
	}
	
	minMs := e.antiBotConfig.MinDelay.Milliseconds()
	maxMs := e.antiBotConfig.MaxDelay.Milliseconds()
	
	if maxMs <= minMs {
		time.Sleep(e.antiBotConfig.MinDelay)
		return
	}
	
	// 简单的随机延迟实现
	delayMs := minMs + (time.Now().UnixNano()%(maxMs-minMs))
	time.Sleep(time.Duration(delayMs) * time.Millisecond)
}

// parseContent 解析内容
func (e *StaticEngine) parseContent(html, url string, statusCode int) (*services.FetchResult, error) {
	// 使用goquery解析HTML
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(html))
	if err != nil {
		return nil, fmt.Errorf("failed to parse HTML: %w", err)
	}
	
	// 提取基本信息
	title := doc.Find("title").First().Text()
	title = strings.TrimSpace(title)
	
	// 使用readability提取主要内容
	article, err := readability.FromReader(strings.NewReader(html), url)
	if err != nil {
		logrus.Warnf("Readability extraction failed for %s: %v", url, err)
		// 如果readability失败，使用简单的文本提取
		article.TextContent = doc.Find("body").Text()
		article.Content = html
	}
	
	// 提取链接
	links := e.extractLinks(doc, url)
	
	// 提取图片
	images := e.extractImages(doc, url)
	
	// 提取元数据
	metadata := e.extractMetadata(doc)
	
	return &services.FetchResult{
		URL:        url,
		Title:      title,
		Content:    article.Content,
		PlainText:  strings.TrimSpace(article.TextContent),
		Metadata:   metadata,
		Links:      links,
		Images:     images,
		StatusCode: statusCode,
		FetchTime:  time.Now(),
	}, nil
}

// extractLinks 提取链接
func (e *StaticEngine) extractLinks(doc *goquery.Document, baseURL string) []services.Link {
	var links []services.Link
	
	doc.Find("a[href]").Each(func(i int, s *goquery.Selection) {
		href, exists := s.Attr("href")
		if !exists {
			return
		}
		
		// 解析相对URL
		absoluteURL := resolveURL(baseURL, href)
		if absoluteURL == "" {
			return
		}
		
		text := strings.TrimSpace(s.Text())
		title, _ := s.Attr("title")
		rel, _ := s.Attr("rel")
		
		links = append(links, services.Link{
			URL:   absoluteURL,
			Text:  text,
			Title: title,
			Rel:   rel,
		})
	})
	
	return links
}

// extractImages 提取图片
func (e *StaticEngine) extractImages(doc *goquery.Document, baseURL string) []services.Image {
	var images []services.Image
	
	doc.Find("img[src]").Each(func(i int, s *goquery.Selection) {
		src, exists := s.Attr("src")
		if !exists {
			return
		}
		
		// 解析相对URL
		absoluteURL := resolveURL(baseURL, src)
		if absoluteURL == "" {
			return
		}
		
		alt, _ := s.Attr("alt")
		title, _ := s.Attr("title")
		
		images = append(images, services.Image{
			URL:   absoluteURL,
			Alt:   alt,
			Title: title,
		})
	})
	
	return images
}

// extractMetadata 提取元数据
func (e *StaticEngine) extractMetadata(doc *goquery.Document) map[string]string {
	metadata := make(map[string]string)
	
	// 提取meta标签
	doc.Find("meta").Each(func(i int, s *goquery.Selection) {
		name, nameExists := s.Attr("name")
		property, propertyExists := s.Attr("property")
		content, contentExists := s.Attr("content")
		
		if !contentExists {
			return
		}
		
		if nameExists {
			metadata[name] = content
		}
		if propertyExists {
			metadata[property] = content
		}
	})
	
	return metadata
}

// 辅助函数
func extractDomain(rawURL string) string {
	u, err := url.Parse(rawURL)
	if err != nil {
		return ""
	}
	return u.Host
}

func resolveURL(baseURL, href string) string {
	base, err := url.Parse(baseURL)
	if err != nil {
		return ""
	}
	
	ref, err := url.Parse(href)
	if err != nil {
		return ""
	}
	
	resolved := base.ResolveReference(ref)
	return resolved.String()
}
