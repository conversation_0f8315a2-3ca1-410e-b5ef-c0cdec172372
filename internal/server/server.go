package server

import (
	"context"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/meizu/mcp-simply-fetch/internal/config"
	"github.com/meizu/mcp-simply-fetch/internal/services"
	"github.com/sirupsen/logrus"
)

// MCPServer MCP服务器
type MCPServer struct {
	server   *server.MCPServer
	config   *config.Config
	services *services.Services
}

// NewMCPServer 创建新的MCP服务器
func NewMCPServer(cfg *config.Config) (*MCPServer, error) {
	// 创建服务层
	services, err := services.NewServices(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create services: %w", err)
	}

	// 创建MCP服务器实例
	mcpServer := server.NewMCPServer(
		cfg.Server.Name,
		cfg.Server.Version,
		server.WithToolCapabilities(true),
		server.WithResourceCapabilities(false, false),
		server.WithPromptCapabilities(false),
		server.WithRecovery(),
	)

	s := &MCPServer{
		server:   mcpServer,
		config:   cfg,
		services: services,
	}

	// 注册工具
	if err := s.registerTools(); err != nil {
		return nil, fmt.Errorf("failed to register tools: %w", err)
	}

	return s, nil
}

// Start 启动服务器
func (s *MCPServer) Start(ctx context.Context) error {
	logrus.Info("Starting MCP server...")

	// 启动服务层
	if err := s.services.Start(ctx); err != nil {
		return fmt.Errorf("failed to start services: %w", err)
	}

	// 启动MCP服务器 (stdio模式)
	if err := server.ServeStdio(s.server); err != nil {
		return fmt.Errorf("MCP server error: %w", err)
	}

	return nil
}

// registerTools 注册MCP工具
func (s *MCPServer) registerTools() error {
	tools := []struct {
		tool    mcp.Tool
		handler server.ToolHandler
	}{
		{
			tool:    s.createFetchURLTool(),
			handler: s.handleFetchURL,
		},
		{
			tool:    s.createFetchBatchTool(),
			handler: s.handleFetchBatch,
		},
		{
			tool:    s.createManageProxyTool(),
			handler: s.handleManageProxy,
		},
		{
			tool:    s.createManageAuthTool(),
			handler: s.handleManageAuth,
		},
		{
			tool:    s.createManageCacheTool(),
			handler: s.handleManageCache,
		},
	}

	for _, t := range tools {
		s.server.AddTool(t.tool, t.handler)
		logrus.Debugf("Registered tool: %s", t.tool.Name)
	}

	return nil
}

// createFetchURLTool 创建单URL抓取工具
func (s *MCPServer) createFetchURLTool() mcp.Tool {
	return mcp.NewTool("fetch_url",
		mcp.WithDescription("抓取单个URL并返回结构化内容"),
		mcp.WithString("url",
			mcp.Required(),
			mcp.Description("目标URL"),
			mcp.Pattern("^https?://.*"),
		),
		mcp.WithString("render_mode",
			mcp.DefaultString("auto"),
			mcp.Description("渲染模式"),
			mcp.Enum("static", "dynamic", "auto"),
		),
		mcp.WithBoolean("use_cache",
			mcp.DefaultBoolean(true),
			mcp.Description("是否使用缓存"),
		),
		mcp.WithBoolean("use_proxy",
			mcp.DefaultBoolean(false),
			mcp.Description("是否使用代理"),
		),
		mcp.WithObject("options",
			mcp.Description("额外选项"),
			mcp.WithProperty("timeout", mcp.NewNumberSchema(mcp.Description("超时时间(秒)"))),
			mcp.WithProperty("headers", mcp.NewObjectSchema(mcp.Description("自定义请求头"))),
		),
	)
}

// createFetchBatchTool 创建批量抓取工具
func (s *MCPServer) createFetchBatchTool() mcp.Tool {
	return mcp.NewTool("fetch_batch",
		mcp.WithDescription("批量抓取多个URL"),
		mcp.WithArray("urls",
			mcp.Required(),
			mcp.Description("URL列表"),
			mcp.Items(mcp.NewStringSchema(mcp.Pattern("^https?://.*"))),
		),
		mcp.WithNumber("concurrency",
			mcp.DefaultNumber(3),
			mcp.Description("并发数"),
			mcp.Minimum(1),
			mcp.Maximum(10),
		),
		mcp.WithString("render_mode",
			mcp.DefaultString("auto"),
			mcp.Description("渲染模式"),
			mcp.Enum("static", "dynamic", "auto"),
		),
		mcp.WithBoolean("use_cache",
			mcp.DefaultBoolean(true),
			mcp.Description("是否使用缓存"),
		),
		mcp.WithBoolean("use_proxy",
			mcp.DefaultBoolean(false),
			mcp.Description("是否使用代理"),
		),
	)
}

// createManageProxyTool 创建代理管理工具
func (s *MCPServer) createManageProxyTool() mcp.Tool {
	return mcp.NewTool("manage_proxy",
		mcp.WithDescription("管理代理配置"),
		mcp.WithString("action",
			mcp.Required(),
			mcp.Description("操作类型"),
			mcp.Enum("add", "remove", "list", "test", "enable", "disable"),
		),
		mcp.WithString("proxy_url",
			mcp.Description("代理URL (格式: *********************:port)"),
		),
		mcp.WithString("proxy_type",
			mcp.DefaultString("http"),
			mcp.Description("代理类型"),
			mcp.Enum("http", "https", "socks5"),
		),
	)
}

// createManageAuthTool 创建认证管理工具
func (s *MCPServer) createManageAuthTool() mcp.Tool {
	return mcp.NewTool("manage_auth",
		mcp.WithDescription("管理网站认证信息"),
		mcp.WithString("action",
			mcp.Required(),
			mcp.Description("操作类型"),
			mcp.Enum("add", "remove", "list", "update", "test"),
		),
		mcp.WithString("domain",
			mcp.Description("目标域名"),
		),
		mcp.WithString("auth_type",
			mcp.DefaultString("cookie"),
			mcp.Description("认证类型"),
			mcp.Enum("cookie", "basic", "bearer", "custom"),
		),
		mcp.WithObject("credentials",
			mcp.Description("认证信息"),
		),
	)
}

// createManageCacheTool 创建缓存管理工具
func (s *MCPServer) createManageCacheTool() mcp.Tool {
	return mcp.NewTool("manage_cache",
		mcp.WithDescription("管理内容缓存"),
		mcp.WithString("action",
			mcp.Required(),
			mcp.Description("操作类型"),
			mcp.Enum("clear", "stats", "cleanup", "get", "delete"),
		),
		mcp.WithString("pattern",
			mcp.Description("URL模式或具体URL"),
		),
		mcp.WithBoolean("force",
			mcp.DefaultBoolean(false),
			mcp.Description("强制执行"),
		),
	)
}

// handleFetchURL 处理单URL抓取
func (s *MCPServer) handleFetchURL(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	url, err := request.RequireString("url")
	if err != nil {
		return mcp.NewToolResultError("missing required parameter: url"), nil
	}

	renderMode := request.GetString("render_mode", "auto")
	useCache := request.GetBoolean("use_cache", true)
	useProxy := request.GetBoolean("use_proxy", false)

	// 构建抓取选项
	options := &services.FetchOptions{
		URL:        url,
		RenderMode: renderMode,
		UseCache:   useCache,
		UseProxy:   useProxy,
	}

	// 处理额外选项
	if optionsObj := request.GetObject("options"); optionsObj != nil {
		if timeout, ok := optionsObj["timeout"].(float64); ok {
			options.Timeout = int(timeout)
		}
		if headers, ok := optionsObj["headers"].(map[string]interface{}); ok {
			options.Headers = make(map[string]string)
			for k, v := range headers {
				if str, ok := v.(string); ok {
					options.Headers[k] = str
				}
			}
		}
	}

	// 执行抓取
	result, err := s.services.FetchService.FetchURL(ctx, options)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("fetch failed: %v", err)), nil
	}

	return mcp.NewToolResultJSON(result), nil
}

// handleFetchBatch 处理批量抓取
func (s *MCPServer) handleFetchBatch(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	urls, err := request.RequireArray("urls")
	if err != nil {
		return mcp.NewToolResultError("missing required parameter: urls"), nil
	}

	concurrency := int(request.GetNumber("concurrency", 3))
	renderMode := request.GetString("render_mode", "auto")
	useCache := request.GetBoolean("use_cache", true)
	useProxy := request.GetBoolean("use_proxy", false)

	// 转换URL列表
	urlList := make([]string, 0, len(urls))
	for _, u := range urls {
		if url, ok := u.(string); ok {
			urlList = append(urlList, url)
		}
	}

	if len(urlList) == 0 {
		return mcp.NewToolResultError("no valid URLs provided"), nil
	}

	// 构建批量抓取选项
	options := &services.BatchFetchOptions{
		URLs:        urlList,
		Concurrency: concurrency,
		RenderMode:  renderMode,
		UseCache:    useCache,
		UseProxy:    useProxy,
	}

	// 执行批量抓取
	results, err := s.services.FetchService.FetchBatch(ctx, options)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("batch fetch failed: %v", err)), nil
	}

	return mcp.NewToolResultJSON(results), nil
}

// handleManageProxy 处理代理管理
func (s *MCPServer) handleManageProxy(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	action, err := request.RequireString("action")
	if err != nil {
		return mcp.NewToolResultError("missing required parameter: action"), nil
	}

	switch action {
	case "list":
		proxies := s.services.ProxyService.ListProxies()
		return mcp.NewToolResultJSON(map[string]interface{}{
			"action":  "list",
			"proxies": proxies,
		}), nil

	case "add":
		proxyURL := request.GetString("proxy_url", "")
		if proxyURL == "" {
			return mcp.NewToolResultError("proxy_url is required for add action"), nil
		}

		proxyType := request.GetString("proxy_type", "http")
		if err := s.services.ProxyService.AddProxy(proxyURL, proxyType); err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("failed to add proxy: %v", err)), nil
		}

		return mcp.NewToolResultText(fmt.Sprintf("Proxy added successfully: %s", proxyURL)), nil

	case "remove":
		proxyURL := request.GetString("proxy_url", "")
		if proxyURL == "" {
			return mcp.NewToolResultError("proxy_url is required for remove action"), nil
		}

		if err := s.services.ProxyService.RemoveProxy(proxyURL); err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("failed to remove proxy: %v", err)), nil
		}

		return mcp.NewToolResultText(fmt.Sprintf("Proxy removed successfully: %s", proxyURL)), nil

	case "test":
		proxyURL := request.GetString("proxy_url", "")
		if proxyURL == "" {
			return mcp.NewToolResultError("proxy_url is required for test action"), nil
		}

		result := s.services.ProxyService.TestProxy(ctx, proxyURL)
		return mcp.NewToolResultJSON(result), nil

	case "enable":
		s.services.ProxyService.Enable()
		return mcp.NewToolResultText("Proxy service enabled"), nil

	case "disable":
		s.services.ProxyService.Disable()
		return mcp.NewToolResultText("Proxy service disabled"), nil

	default:
		return mcp.NewToolResultError(fmt.Sprintf("unknown action: %s", action)), nil
	}
}

// handleManageAuth 处理认证管理
func (s *MCPServer) handleManageAuth(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	action, err := request.RequireString("action")
	if err != nil {
		return mcp.NewToolResultError("missing required parameter: action"), nil
	}

	switch action {
	case "list":
		auths := s.services.AuthService.ListDomains()
		return mcp.NewToolResultJSON(map[string]interface{}{
			"action":  "list",
			"domains": auths,
		}), nil

	case "add", "update":
		domain := request.GetString("domain", "")
		if domain == "" {
			return mcp.NewToolResultError("domain is required"), nil
		}

		authType := request.GetString("auth_type", "cookie")
		credentials := request.GetObject("credentials")
		if credentials == nil {
			return mcp.NewToolResultError("credentials are required"), nil
		}

		if err := s.services.AuthService.SetAuth(domain, authType, credentials); err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("failed to set auth: %v", err)), nil
		}

		return mcp.NewToolResultText(fmt.Sprintf("Authentication %s for domain: %s", action, domain)), nil

	case "remove":
		domain := request.GetString("domain", "")
		if domain == "" {
			return mcp.NewToolResultError("domain is required for remove action"), nil
		}

		if err := s.services.AuthService.RemoveAuth(domain); err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("failed to remove auth: %v", err)), nil
		}

		return mcp.NewToolResultText(fmt.Sprintf("Authentication removed for domain: %s", domain)), nil

	case "test":
		domain := request.GetString("domain", "")
		if domain == "" {
			return mcp.NewToolResultError("domain is required for test action"), nil
		}

		result := s.services.AuthService.TestAuth(ctx, domain)
		return mcp.NewToolResultJSON(result), nil

	default:
		return mcp.NewToolResultError(fmt.Sprintf("unknown action: %s", action)), nil
	}
}

// handleManageCache 处理缓存管理
func (s *MCPServer) handleManageCache(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	action, err := request.RequireString("action")
	if err != nil {
		return mcp.NewToolResultError("missing required parameter: action"), nil
	}

	switch action {
	case "stats":
		stats := s.services.CacheService.GetStats()
		return mcp.NewToolResultJSON(stats), nil

	case "clear":
		pattern := request.GetString("pattern", "")
		force := request.GetBoolean("force", false)

		count, err := s.services.CacheService.Clear(pattern, force)
		if err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("failed to clear cache: %v", err)), nil
		}

		return mcp.NewToolResultText(fmt.Sprintf("Cleared %d cache entries", count)), nil

	case "cleanup":
		count, err := s.services.CacheService.Cleanup()
		if err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("failed to cleanup cache: %v", err)), nil
		}

		return mcp.NewToolResultText(fmt.Sprintf("Cleaned up %d expired cache entries", count)), nil

	case "get":
		pattern := request.GetString("pattern", "")
		if pattern == "" {
			return mcp.NewToolResultError("pattern is required for get action"), nil
		}

		result, err := s.services.CacheService.Get(pattern)
		if err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("failed to get cache: %v", err)), nil
		}

		return mcp.NewToolResultJSON(result), nil

	case "delete":
		pattern := request.GetString("pattern", "")
		if pattern == "" {
			return mcp.NewToolResultError("pattern is required for delete action"), nil
		}

		if err := s.services.CacheService.Delete(pattern); err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("failed to delete cache: %v", err)), nil
		}

		return mcp.NewToolResultText(fmt.Sprintf("Cache deleted for pattern: %s", pattern)), nil

	default:
		return mcp.NewToolResultError(fmt.Sprintf("unknown action: %s", action)), nil
	}
}
