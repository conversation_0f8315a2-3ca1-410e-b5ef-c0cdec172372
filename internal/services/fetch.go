package services

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/meizu/mcp-simply-fetch/internal/config"
	"github.com/meizu/mcp-simply-fetch/internal/engines"
	"github.com/sirupsen/logrus"
	"golang.org/x/sync/errgroup"
)

// FetchService 抓取服务
type FetchService struct {
	config       config.FetcherConfig
	antiBotConfig config.AntiBotConfig
	cacheService *CacheService
	authService  *AuthService
	proxyService *ProxyService
	
	staticEngine  *engines.StaticEngine
	dynamicEngine *engines.DynamicEngine
	
	mu sync.RWMutex
}

// NewFetchService 创建抓取服务
func NewFetchService(
	fetcherConfig config.FetcherConfig,
	antiBotConfig config.AntiBotConfig,
	cacheService *CacheService,
	authService *AuthService,
	proxyService *ProxyService,
) (*FetchService, error) {
	
	// 创建静态抓取引擎
	staticEngine, err := engines.NewStaticEngine(fetcherConfig, antiBotConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create static engine: %w", err)
	}
	
	// 创建动态抓取引擎
	dynamicEngine, err := engines.NewDynamicEngine(fetcherConfig, antiBotConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create dynamic engine: %w", err)
	}
	
	return &FetchService{
		config:        fetcherConfig,
		antiBotConfig: antiBotConfig,
		cacheService:  cacheService,
		authService:   authService,
		proxyService:  proxyService,
		staticEngine:  staticEngine,
		dynamicEngine: dynamicEngine,
	}, nil
}

// Start 启动服务
func (f *FetchService) Start(ctx context.Context) error {
	logrus.Info("Starting fetch service...")
	
	// 启动抓取引擎
	if err := f.staticEngine.Start(ctx); err != nil {
		return fmt.Errorf("failed to start static engine: %w", err)
	}
	
	if err := f.dynamicEngine.Start(ctx); err != nil {
		return fmt.Errorf("failed to start dynamic engine: %w", err)
	}
	
	logrus.Info("Fetch service started")
	return nil
}

// Stop 停止服务
func (f *FetchService) Stop(ctx context.Context) error {
	logrus.Info("Stopping fetch service...")
	
	if err := f.dynamicEngine.Stop(ctx); err != nil {
		logrus.Errorf("Failed to stop dynamic engine: %v", err)
	}
	
	if err := f.staticEngine.Stop(ctx); err != nil {
		logrus.Errorf("Failed to stop static engine: %v", err)
	}
	
	logrus.Info("Fetch service stopped")
	return nil
}

// FetchURL 抓取单个URL
func (f *FetchService) FetchURL(ctx context.Context, options *FetchOptions) (*FetchResult, error) {
	startTime := time.Now()
	
	// 验证URL
	if err := f.validateURL(options.URL); err != nil {
		return nil, fmt.Errorf("invalid URL: %w", err)
	}
	
	// 检查缓存
	if options.UseCache {
		if cached, err := f.cacheService.Get(options.URL); err == nil && cached != nil {
			if result, ok := cached.(*FetchResult); ok {
				result.CacheHit = true
				result.Duration = time.Since(startTime)
				logrus.Debugf("Cache hit for URL: %s", options.URL)
				return result, nil
			}
		}
	}
	
	// 选择渲染策略
	strategy := f.selectRenderStrategy(options.URL, options.RenderMode)
	
	// 准备抓取上下文
	fetchCtx, err := f.prepareFetchContext(ctx, options)
	if err != nil {
		return nil, fmt.Errorf("failed to prepare fetch context: %w", err)
	}
	
	// 执行抓取
	var result *FetchResult
	switch strategy {
	case RenderStrategyStatic:
		result, err = f.fetchStatic(fetchCtx, options)
	case RenderStrategyDynamic:
		result, err = f.fetchDynamic(fetchCtx, options)
	case RenderStrategyAuto:
		// 先尝试静态，失败后尝试动态
		result, err = f.fetchStatic(fetchCtx, options)
		if err != nil || f.needsDynamicRender(result) {
			logrus.Debugf("Static fetch failed or needs dynamic render, trying dynamic for: %s", options.URL)
			result, err = f.fetchDynamic(fetchCtx, options)
		}
	default:
		return nil, fmt.Errorf("unknown render strategy: %v", strategy)
	}
	
	if err != nil {
		return nil, fmt.Errorf("fetch failed: %w", err)
	}
	
	// 设置结果元数据
	result.Duration = time.Since(startTime)
	result.FetchTime = startTime
	result.RenderMode = strategy.String()
	result.CacheHit = false
	
	// 缓存结果
	if options.UseCache && result.StatusCode == 200 {
		if err := f.cacheService.Set(options.URL, result); err != nil {
			logrus.Warnf("Failed to cache result for %s: %v", options.URL, err)
		}
	}
	
	return result, nil
}

// FetchBatch 批量抓取URL
func (f *FetchService) FetchBatch(ctx context.Context, options *BatchFetchOptions) (*BatchFetchResult, error) {
	startTime := time.Now()
	
	if len(options.URLs) == 0 {
		return nil, fmt.Errorf("no URLs provided")
	}
	
	// 限制并发数
	concurrency := options.Concurrency
	if concurrency <= 0 {
		concurrency = f.config.Concurrency
	}
	if concurrency > len(options.URLs) {
		concurrency = len(options.URLs)
	}
	
	// 创建结果通道
	results := make([]FetchResult, len(options.URLs))
	
	// 使用errgroup进行并发控制
	g, ctx := errgroup.WithContext(ctx)
	g.SetLimit(concurrency)
	
	// 并发抓取
	for i, targetURL := range options.URLs {
		i, targetURL := i, targetURL // 避免闭包问题
		
		g.Go(func() error {
			fetchOptions := &FetchOptions{
				URL:        targetURL,
				RenderMode: options.RenderMode,
				UseCache:   options.UseCache,
				UseProxy:   options.UseProxy,
			}
			
			result, err := f.FetchURL(ctx, fetchOptions)
			if err != nil {
				// 记录错误但不中断其他任务
				results[i] = FetchResult{
					URL:       targetURL,
					Error:     err.Error(),
					FetchTime: time.Now(),
				}
				logrus.Warnf("Failed to fetch %s: %v", targetURL, err)
			} else {
				results[i] = *result
			}
			
			return nil // 不返回错误，让其他任务继续
		})
	}
	
	// 等待所有任务完成
	if err := g.Wait(); err != nil {
		return nil, fmt.Errorf("batch fetch error: %w", err)
	}
	
	// 统计结果
	var success, failed int
	for _, result := range results {
		if result.Error == "" && result.StatusCode == 200 {
			success++
		} else {
			failed++
		}
	}
	
	return &BatchFetchResult{
		Results:   results,
		Total:     len(options.URLs),
		Success:   success,
		Failed:    failed,
		Duration:  time.Since(startTime),
		StartTime: startTime,
		EndTime:   time.Now(),
	}, nil
}

// validateURL 验证URL格式
func (f *FetchService) validateURL(rawURL string) error {
	u, err := url.Parse(rawURL)
	if err != nil {
		return fmt.Errorf("invalid URL format: %w", err)
	}
	
	if u.Scheme != "http" && u.Scheme != "https" {
		return fmt.Errorf("unsupported URL scheme: %s", u.Scheme)
	}
	
	if u.Host == "" {
		return fmt.Errorf("missing host in URL")
	}
	
	return nil
}

// selectRenderStrategy 选择渲染策略
func (f *FetchService) selectRenderStrategy(url, mode string) RenderStrategy {
	strategy := ParseRenderStrategy(mode)
	
	if strategy != RenderStrategyAuto {
		return strategy
	}
	
	// 基于URL特征自动选择策略
	if f.isStaticSite(url) {
		return RenderStrategyStatic
	}
	
	if f.requiresJavaScript(url) {
		return RenderStrategyDynamic
	}
	
	// 默认使用自动策略（先静态后动态）
	return RenderStrategyAuto
}

// isStaticSite 判断是否为静态站点
func (f *FetchService) isStaticSite(url string) bool {
	// 简单的静态站点判断逻辑
	staticPatterns := []string{
		"github.com",
		"stackoverflow.com",
		"wikipedia.org",
		"reddit.com",
	}
	
	for _, pattern := range staticPatterns {
		if strings.Contains(url, pattern) {
			return true
		}
	}
	
	return false
}

// requiresJavaScript 判断是否需要JavaScript渲染
func (f *FetchService) requiresJavaScript(url string) bool {
	// 需要JavaScript的站点模式
	jsPatterns := []string{
		"spa",
		"react",
		"vue",
		"angular",
		"app",
	}
	
	urlLower := strings.ToLower(url)
	for _, pattern := range jsPatterns {
		if strings.Contains(urlLower, pattern) {
			return true
		}
	}
	
	return false
}

// needsDynamicRender 判断静态抓取结果是否需要动态渲染
func (f *FetchService) needsDynamicRender(result *FetchResult) bool {
	if result == nil {
		return true
	}
	
	// 如果内容太少，可能需要JavaScript渲染
	if len(result.PlainText) < 100 {
		return true
	}
	
	// 如果包含常见的SPA标识，需要动态渲染
	content := strings.ToLower(result.Content)
	spaIndicators := []string{
		"loading...",
		"please enable javascript",
		"javascript is required",
		"<div id=\"root\">",
		"<div id=\"app\">",
	}
	
	for _, indicator := range spaIndicators {
		if strings.Contains(content, indicator) {
			return true
		}
	}
	
	return false
}

// prepareFetchContext 准备抓取上下文
func (f *FetchService) prepareFetchContext(ctx context.Context, options *FetchOptions) (context.Context, error) {
	// 设置超时
	timeout := time.Duration(options.Timeout) * time.Second
	if timeout <= 0 {
		timeout = f.config.Timeout
	}
	
	ctx, cancel := context.WithTimeout(ctx, timeout)
	_ = cancel // 由调用者负责取消
	
	return ctx, nil
}

// fetchStatic 静态抓取
func (f *FetchService) fetchStatic(ctx context.Context, options *FetchOptions) (*FetchResult, error) {
	return f.staticEngine.Fetch(ctx, options, f.authService, f.proxyService)
}

// fetchDynamic 动态抓取
func (f *FetchService) fetchDynamic(ctx context.Context, options *FetchOptions) (*FetchResult, error) {
	return f.dynamicEngine.Fetch(ctx, options, f.authService, f.proxyService)
}
