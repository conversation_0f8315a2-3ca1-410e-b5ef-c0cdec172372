package services

import (
	"time"
)

// FetchOptions 抓取选项
type FetchOptions struct {
	URL        string            `json:"url"`
	RenderMode string            `json:"render_mode"` // static, dynamic, auto
	UseCache   bool              `json:"use_cache"`
	UseProxy   bool              `json:"use_proxy"`
	Timeout    int               `json:"timeout"`     // 超时时间(秒)
	Headers    map[string]string `json:"headers"`     // 自定义请求头
	UserAgent  string            `json:"user_agent"`  // 自定义User-Agent
}

// BatchFetchOptions 批量抓取选项
type BatchFetchOptions struct {
	URLs        []string `json:"urls"`
	Concurrency int      `json:"concurrency"`
	RenderMode  string   `json:"render_mode"`
	UseCache    bool     `json:"use_cache"`
	UseProxy    bool     `json:"use_proxy"`
}

// FetchResult 抓取结果
type FetchResult struct {
	URL           string            `json:"url"`
	Title         string            `json:"title"`
	Content       string            `json:"content"`
	PlainText     string            `json:"plain_text"`
	Metadata      map[string]string `json:"metadata"`
	Links         []Link            `json:"links"`
	Images        []Image           `json:"images"`
	StatusCode    int               `json:"status_code"`
	FetchTime     time.Time         `json:"fetch_time"`
	RenderMode    string            `json:"render_mode"`
	CacheHit      bool              `json:"cache_hit"`
	Error         string            `json:"error,omitempty"`
	Duration      time.Duration     `json:"duration"`
}

// BatchFetchResult 批量抓取结果
type BatchFetchResult struct {
	Results   []FetchResult `json:"results"`
	Total     int           `json:"total"`
	Success   int           `json:"success"`
	Failed    int           `json:"failed"`
	Duration  time.Duration `json:"duration"`
	StartTime time.Time     `json:"start_time"`
	EndTime   time.Time     `json:"end_time"`
}

// Link 链接信息
type Link struct {
	URL    string `json:"url"`
	Text   string `json:"text"`
	Title  string `json:"title"`
	Rel    string `json:"rel,omitempty"`
}

// Image 图片信息
type Image struct {
	URL    string `json:"url"`
	Alt    string `json:"alt"`
	Title  string `json:"title,omitempty"`
	Width  int    `json:"width,omitempty"`
	Height int    `json:"height,omitempty"`
}

// ProxyInfo 代理信息
type ProxyInfo struct {
	URL       string        `json:"url"`
	Type      string        `json:"type"`      // http, https, socks5
	Status    string        `json:"status"`    // active, inactive, error
	LastCheck time.Time     `json:"last_check"`
	Latency   time.Duration `json:"latency"`
	ErrorMsg  string        `json:"error_msg,omitempty"`
}

// ProxyTestResult 代理测试结果
type ProxyTestResult struct {
	URL      string        `json:"url"`
	Success  bool          `json:"success"`
	Latency  time.Duration `json:"latency"`
	Error    string        `json:"error,omitempty"`
	TestTime time.Time     `json:"test_time"`
}

// AuthInfo 认证信息
type AuthInfo struct {
	Domain      string            `json:"domain"`
	Type        string            `json:"type"`        // cookie, basic, bearer, custom
	Credentials map[string]string `json:"credentials"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	LastUsed    time.Time         `json:"last_used"`
}

// AuthTestResult 认证测试结果
type AuthTestResult struct {
	Domain   string    `json:"domain"`
	Success  bool      `json:"success"`
	Error    string    `json:"error,omitempty"`
	TestTime time.Time `json:"test_time"`
}

// CacheStats 缓存统计
type CacheStats struct {
	TotalEntries   int64         `json:"total_entries"`
	TotalSize      int64         `json:"total_size"`
	HitRate        float64       `json:"hit_rate"`
	MissRate       float64       `json:"miss_rate"`
	OldestEntry    time.Time     `json:"oldest_entry"`
	NewestEntry    time.Time     `json:"newest_entry"`
	AvgEntrySize   int64         `json:"avg_entry_size"`
	DatabaseSize   int64         `json:"database_size"`
	LastCleanup    time.Time     `json:"last_cleanup"`
	CleanupCount   int64         `json:"cleanup_count"`
}

// CacheEntry 缓存条目
type CacheEntry struct {
	URL       string      `json:"url"`
	Content   interface{} `json:"content"`
	CreatedAt time.Time   `json:"created_at"`
	ExpiresAt time.Time   `json:"expires_at"`
	Size      int64       `json:"size"`
	HitCount  int64       `json:"hit_count"`
}

// RenderStrategy 渲染策略
type RenderStrategy int

const (
	RenderStrategyAuto RenderStrategy = iota
	RenderStrategyStatic
	RenderStrategyDynamic
)

func (r RenderStrategy) String() string {
	switch r {
	case RenderStrategyAuto:
		return "auto"
	case RenderStrategyStatic:
		return "static"
	case RenderStrategyDynamic:
		return "dynamic"
	default:
		return "unknown"
	}
}

// ParseRenderStrategy 解析渲染策略
func ParseRenderStrategy(s string) RenderStrategy {
	switch s {
	case "static":
		return RenderStrategyStatic
	case "dynamic":
		return RenderStrategyDynamic
	case "auto":
		return RenderStrategyAuto
	default:
		return RenderStrategyAuto
	}
}
