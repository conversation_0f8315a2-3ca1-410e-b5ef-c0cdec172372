package services

import (
	"context"
	"fmt"

	"github.com/meizu/mcp-simply-fetch/internal/config"
	"github.com/sirupsen/logrus"
)

// Services 服务集合
type Services struct {
	FetchService *FetchService
	AuthService  *AuthService
	ProxyService *ProxyService
	CacheService *CacheService
	config       *config.Config
}

// NewServices 创建服务集合
func NewServices(cfg *config.Config) (*Services, error) {
	// 创建缓存服务
	cacheService, err := NewCacheService(cfg.Cache)
	if err != nil {
		return nil, fmt.Errorf("failed to create cache service: %w", err)
	}

	// 创建认证服务
	authService, err := NewAuthService(cfg.Auth)
	if err != nil {
		return nil, fmt.Errorf("failed to create auth service: %w", err)
	}

	// 创建代理服务
	proxyService, err := NewProxyService(cfg.Proxy)
	if err != nil {
		return nil, fmt.Errorf("failed to create proxy service: %w", err)
	}

	// 创建抓取服务
	fetchService, err := NewFetchService(cfg.Fetcher, cfg.AntiBot, cacheService, authService, proxyService)
	if err != nil {
		return nil, fmt.Errorf("failed to create fetch service: %w", err)
	}

	return &Services{
		FetchService: fetchService,
		AuthService:  authService,
		ProxyService: proxyService,
		CacheService: cacheService,
		config:       cfg,
	}, nil
}

// Start 启动所有服务
func (s *Services) Start(ctx context.Context) error {
	logrus.Info("Starting services...")

	// 启动缓存服务
	if err := s.CacheService.Start(ctx); err != nil {
		return fmt.Errorf("failed to start cache service: %w", err)
	}

	// 启动认证服务
	if err := s.AuthService.Start(ctx); err != nil {
		return fmt.Errorf("failed to start auth service: %w", err)
	}

	// 启动代理服务
	if err := s.ProxyService.Start(ctx); err != nil {
		return fmt.Errorf("failed to start proxy service: %w", err)
	}

	// 启动抓取服务
	if err := s.FetchService.Start(ctx); err != nil {
		return fmt.Errorf("failed to start fetch service: %w", err)
	}

	logrus.Info("All services started successfully")
	return nil
}

// Stop 停止所有服务
func (s *Services) Stop(ctx context.Context) error {
	logrus.Info("Stopping services...")

	// 按相反顺序停止服务
	if err := s.FetchService.Stop(ctx); err != nil {
		logrus.Errorf("Failed to stop fetch service: %v", err)
	}

	if err := s.ProxyService.Stop(ctx); err != nil {
		logrus.Errorf("Failed to stop proxy service: %v", err)
	}

	if err := s.AuthService.Stop(ctx); err != nil {
		logrus.Errorf("Failed to stop auth service: %v", err)
	}

	if err := s.CacheService.Stop(ctx); err != nil {
		logrus.Errorf("Failed to stop cache service: %v", err)
	}

	logrus.Info("All services stopped")
	return nil
}
