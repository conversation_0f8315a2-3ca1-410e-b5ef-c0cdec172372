package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/meizu/mcp-simply-fetch/internal/config"
	"github.com/meizu/mcp-simply-fetch/internal/server"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
)

var (
	configFile string
	logLevel   string
	version    = "1.0.0"
)

func main() {
	rootCmd := &cobra.Command{
		Use:     "mcp-simply-fetch",
		Short:   "MCP服务：将网页内容转换为结构化数据",
		Long:    `一个基于MCP协议的服务，用于协助AI将网址中的内容转换为结构化内容，支持静态和动态页面抓取、代理、认证等功能。`,
		Version: version,
		Run:     runServer,
	}

	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "", "配置文件路径")
	rootCmd.PersistentFlags().StringVarP(&logLevel, "log-level", "l", "info", "日志级别 (debug, info, warn, error)")

	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func runServer(cmd *cobra.Command, args []string) {
	// 设置日志级别
	level, err := logrus.ParseLevel(logLevel)
	if err != nil {
		log.Fatalf("Invalid log level: %v", err)
	}
	logrus.SetLevel(level)
	logrus.SetFormatter(&logrus.JSONFormatter{})

	// 加载配置
	cfg, err := config.Load(configFile)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 创建MCP服务器
	mcpServer, err := server.NewMCPServer(cfg)
	if err != nil {
		log.Fatalf("Failed to create MCP server: %v", err)
	}

	// 设置优雅关闭
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 监听系统信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		sig := <-sigChan
		logrus.Infof("Received signal: %v", sig)
		cancel()
	}()

	// 启动服务器
	logrus.Infof("Starting MCP Simply Fetch Server v%s", version)
	if err := mcpServer.Start(ctx); err != nil {
		log.Fatalf("Server error: %v", err)
	}

	logrus.Info("Server stopped gracefully")
}
